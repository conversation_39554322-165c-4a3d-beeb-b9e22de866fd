'use client'

import React from 'react'
import { motion } from "framer-motion"
import { Brain, Cpu, GraduationCap, Code, ArrowRight, Sparkles, Zap, Target } from 'lucide-react'
import { Button } from '../ui/button'
import Link from 'next/link'

function ServicesOverview() {
  const services = [
    {
      id: 'ai-annotation',
      title: 'AI智能标注',
      subtitle: '精准数据标注服务',
      description: '提供高精度的多模态数据标注服务，为AI模型训练提供优质数据集，支持图像、文本、语音、视频等多种数据类型。',
      icon: Brain,
      color: 'from-blue-500 to-blue-600',
      bgColor: 'bg-blue-50',
      features: ['图像标注', '文本标注', '语音标注', '视频标注'],
      stats: { accuracy: '99.5%', speed: '10x', clients: '500+' }
    },
    {
      id: 'cpu-rental',
      title: 'CPU算力租用',
      subtitle: '弹性云计算资源',
      description: '提供高性能CPU集群租用服务，支持科学计算、深度学习训练等高算力需求，按需付费，弹性扩容。',
      icon: Cpu,
      color: 'from-indigo-500 to-indigo-600',
      bgColor: 'bg-indigo-50',
      features: ['高性能计算', '弹性扩容', '按需付费', '24/7监控'],
      stats: { performance: '100x', availability: '99.9%', savings: '60%' }
    },
    {
      id: 'education',
      title: '教育培训管理',
      subtitle: '一站式教育平台',
      description: '完整的教育培训管理解决方案，涵盖课程管理、学员管理、在线考试、证书颁发等教育生态链。',
      icon: GraduationCap,
      color: 'from-purple-500 to-purple-600',
      bgColor: 'bg-purple-50',
      features: ['课程管理', '在线考试', '学员跟踪', '证书系统'],
      stats: { students: '10万+', courses: '1000+', satisfaction: '98%' }
    },
    {
      id: 'custom-development',
      title: '定制软件开发',
      subtitle: '专业软件解决方案',
      description: '提供企业级定制软件开发服务，涵盖Web应用、移动应用、桌面软件等全栈开发解决方案。',
      icon: Code,
      color: 'from-emerald-500 to-emerald-600',
      bgColor: 'bg-emerald-50',
      features: ['全栈开发', '移动应用', '系统集成', '技术咨询'],
      stats: { projects: '200+', clients: '150+', satisfaction: '99%' }
    }
  ]

  return (
    <section className="py-24 sm:py-32 relative overflow-hidden">
      {/* 背景装饰 */}
      <div className="absolute inset-0 bg-gradient-to-b from-white via-slate-50/50 to-blue-50/30" />
      <div className="absolute top-0 left-0 w-full h-full">
        <div className="absolute top-20 left-10 w-72 h-72 rounded-full blur-3xl opacity-20 animate-float" style={{ background: 'linear-gradient(135deg, rgb(59 130 246), rgb(37 99 235))' }} />
        <div className="absolute bottom-20 right-10 w-96 h-96 rounded-full blur-3xl opacity-15 animate-float" style={{ background: 'linear-gradient(135deg, rgb(99 102 241), rgb(59 130 246))', animationDelay: '3s' }} />
      </div>

      <div className="relative mx-auto max-w-7xl px-6 lg:px-8">
        {/* 标题区域 */}
        <motion.div
          className="mx-auto max-w-4xl text-center mb-20"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <div className="flex items-center justify-center gap-2 mb-6">
            <Sparkles className="h-6 w-6 text-blue-600" />
            <span className="text-lg font-semibold text-blue-600">核心业务</span>
            <Sparkles className="h-6 w-6 text-blue-600" />
          </div>
          <h2 className="text-4xl font-bold tracking-tight sm:text-5xl lg:text-6xl text-gradient-modern mb-8">
            四大核心业务领域
          </h2>
          <p className="text-xl leading-relaxed text-slate-600 max-w-3xl mx-auto">
            专注于AI智能标注、CPU算力租用、教育培训管理、定制软件开发四大核心业务，为客户提供全方位的数字化解决方案
          </p>
        </motion.div>

        {/* 服务卡片网格 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 lg:gap-6">
          {services.map((service, index) => (
            <motion.div
              key={service.id}
              initial={{ opacity: 0, y: 40 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: index * 0.2 }}
              viewport={{ once: true }}
              className="group"
            >
              <ServiceCard service={service} index={index} />
            </motion.div>
          ))}
        </div>

        {/* 底部CTA */}
        <motion.div
          className="text-center mt-20"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          viewport={{ once: true }}
        >
          <Button asChild className="btn-modern shadow-button-modern group px-8 py-4 text-lg">
            <Link href="/products">
              探索所有产品
              <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
            </Link>
          </Button>
        </motion.div>
      </div>
    </section>
  )
}

// 服务卡片组件
function ServiceCard({ service, index }: { service: any, index: number }) {
  return (
    <div className="relative h-full group">
      <div className="card-modern p-8 h-full hover-glow transition-all duration-500 hover:scale-105">
        {/* 图标和标题 */}
        <div className="flex flex-col items-center text-center mb-8">
          <div className="relative mb-6">
            <div className={`flex h-20 w-20 items-center justify-center rounded-3xl shadow-lg bg-gradient-to-br ${service.color} group-hover:scale-110 transition-transform duration-300`}>
              <service.icon className="h-10 w-10 text-white" />
            </div>
            <div className="absolute inset-0 rounded-3xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" style={{ background: 'rgba(59, 130, 246, 0.3)' }} />
          </div>
          
          <h3 className="text-2xl font-bold text-slate-800 mb-2 group-hover:text-gradient-modern transition-all duration-300">
            {service.title}
          </h3>
          <p className="text-sm font-medium text-blue-600 mb-4">
            {service.subtitle}
          </p>
        </div>

        {/* 描述 */}
        <p className="text-slate-600 leading-relaxed mb-8 text-center">
          {service.description}
        </p>

        {/* 特性标签 */}
        <div className="flex flex-wrap gap-2 justify-center mb-8">
          {service.features.map((feature: string, idx: number) => (
            <span
              key={feature}
              className="inline-flex items-center rounded-full px-3 py-1.5 text-sm font-medium transition-all duration-200 hover-lift"
              style={{
                backgroundColor: 'rgba(59, 130, 246, 0.08)',
                color: 'rgb(59 130 246)',
                animationDelay: `${idx * 100}ms`
              }}
            >
              {feature}
            </span>
          ))}
        </div>

        {/* 统计数据 */}
        <div className="grid grid-cols-3 gap-4 pt-6 border-t border-slate-100">
          {Object.entries(service.stats).map(([key, value], idx) => (
            <div key={key} className="text-center">
              <div className="text-lg font-bold text-blue-600">{value}</div>
              <div className="text-xs text-slate-500 capitalize">{key}</div>
            </div>
          ))}
        </div>

        {/* 悬停效果底边 */}
        <div className="absolute bottom-0 left-0 right-0 h-1 rounded-b-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" style={{ background: `linear-gradient(90deg, ${service.color.replace('from-', '').replace(' to-', ', ')})` }} />
      </div>
    </div>
  )
}

export default ServicesOverview
