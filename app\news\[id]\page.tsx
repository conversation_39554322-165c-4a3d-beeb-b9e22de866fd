import { NewsDetail } from "./components/NewsDetail"
import { notFound } from "next/navigation"

// 本地新闻数据
const newsItems = [
  {
    id: "1",
    title: "零点科技完成新一轮融资，加速AI技术研发",
    content: `
    <p>零点科技今日宣布完成新一轮融资，本轮融资将主要用于AI技术研发和团队扩建。</p>
    <p>作为专注于AI智能标注、CPU算力租用、教育培训管理和定制软件开发的科技公司，零点科技在过去一年中取得了显著的业务增长。</p>
    `,
    date: "2024-03-15",
    category: "公司新闻",
    image: "https://picsum.photos/seed/news1/1200/600",
    author: {
      name: "张三",
      role: "CEO",
      image: "https://picsum.photos/seed/author1/40/40"
    }
  },
  {
    id: "2",
    title: "零点科技荣获「2024年度最具创新力企业」奖项",
    content: `
    <p>零点科技今日宣布荣获「2024年度最具创新力企业」奖项,这是对公司在技术创新和市场应用方面的认可。</p>
    `,
    date: "2024-03-20",
    category: "公司新闻",
    image: "https://picsum.photos/seed/news2/1200/600",
    author: {
      name: "李华",
      role: "公关总监",
      image: "https://picsum.photos/seed/author2/40/40"
    }
  }
]

// 获取单个新闻详情
async function getNewsById(id: string) {
  const news = newsItems.find(item => item.id === id)
  return news || null
}

export async function generateStaticParams() {
  return newsItems.map((news) => ({
    id: news.id,
  }))
}

export default async function NewsPost({ params }: { params: { id: string } }) {
  const news = await getNewsById(params.id)

  if (!news) {
    notFound()
  }

  return <NewsDetail news={news} />
}