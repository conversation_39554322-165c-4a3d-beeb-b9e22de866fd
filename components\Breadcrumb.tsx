'use client'

import Link from 'next/link'
import { ChevronRight, Home } from 'lucide-react'
import { usePathname } from 'next/navigation'

interface BreadcrumbItem {
  label: string
  href: string
}

interface BreadcrumbProps {
  items?: BreadcrumbItem[]
  className?: string
}

export function Breadcrumb({ items, className = '' }: BreadcrumbProps) {
  const pathname = usePathname()
  
  // 如果没有提供items，则根据当前路径自动生成
  const breadcrumbItems = items || generateBreadcrumbItems(pathname)
  
  if (breadcrumbItems.length <= 1) {
    return null // 首页不显示面包屑
  }

  return (
    <nav 
      aria-label="面包屑导航" 
      className={`flex items-center space-x-2 text-sm text-muted-foreground ${className}`}
    >
      <Link 
        href="/" 
        className="flex items-center hover:text-primary transition-colors"
        aria-label="返回首页"
      >
        <Home className="h-4 w-4" />
      </Link>
      
      {breadcrumbItems.slice(1).map((item, index) => (
        <div key={item.href} className="flex items-center space-x-2">
          <ChevronRight className="h-4 w-4" />
          {index === breadcrumbItems.length - 2 ? (
            <span className="text-foreground font-medium" aria-current="page">
              {item.label}
            </span>
          ) : (
            <Link 
              href={item.href} 
              className="hover:text-primary transition-colors"
            >
              {item.label}
            </Link>
          )}
        </div>
      ))}
    </nav>
  )
}

function generateBreadcrumbItems(pathname: string): BreadcrumbItem[] {
  const segments = pathname.split('/').filter(Boolean)
  const items: BreadcrumbItem[] = [{ label: '首页', href: '/' }]
  
  let currentPath = ''
  
  for (const segment of segments) {
    currentPath += `/${segment}`
    
    // 根据路径段生成标签
    let label = segment
    switch (segment) {
      case 'about':
        label = '关于我们'
        break
      case 'products':
        label = '产品服务'
        break
      case 'cases':
        label = '客户案例'
        break
      case 'news':
        label = '新闻动态'
        break
      case 'contact-us':
        label = '联系我们'
        break
      case 'privacy':
        label = '隐私政策'
        break
      case 'cookies':
        label = 'Cookie政策'
        break
      case 'ai-annotation':
        label = 'AI智能标注'
        break
      case 'cpu-rental':
        label = 'CPU算力租用'
        break
      case 'education-management':
        label = '教育培训管理'
        break
      case 'custom-development':
        label = '定制软件开发'
        break
      default:
        // 对于动态路由，保持原始值或进行其他处理
        label = decodeURIComponent(segment)
    }
    
    items.push({ label, href: currentPath })
  }
  
  return items
}

// 生成结构化数据的面包屑
export function generateBreadcrumbStructuredData(items: BreadcrumbItem[]) {
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://0dot.com'
  
  return {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": items.map((item, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "name": item.label,
      "item": `${baseUrl}${item.href}`
    }))
  }
}
