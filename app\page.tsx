
import { Metadata } from 'next'
import { getTranslations } from 'next-intl/server'
import StatsSection from "@/components/home/<USER>"
import HeroSection from "@/components/home/<USER>"
import FeaturesSection from "@/components/home/<USER>"
import SolutionsSection from "@/components/home/<USER>"
import TestimonialsSection from "@/components/home/<USER>"
import PartnersSection from "@/components/home/<USER>"
import CTASection from "@/components/home/<USER>"
import ServicesOverview from "@/components/home/<USER>"
import TechnologyStack from "@/components/home/<USER>"
import ProcessFlow from "@/components/home/<USER>"
import ClientShowcase from "@/components/home/<USER>"


const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://0dot.com';

export async function generateMetadata({ params: { locale } }: { params: { locale: string } }): Promise<Metadata> {
  const t = await getTranslations({ locale, namespace: 'hero' });

  return {
    title: t('title'),
    description: t('description'),
    openGraph: {
      title: t('title'),
      description: t('subtitle'),
      url: baseUrl,
      siteName: '0dot',
      images: [
        {
          url: `${baseUrl}/og-home.jpg`,
          width: 1200,
          height: 630,
          alt: t('title'),
        },
      ],
      locale: locale === 'zh' ? 'zh_CN' : 'en_US',
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title: t('title'),
      description: t('subtitle'),
      images: [`${baseUrl}/og-home.jpg`],
    },
    alternates: {
      canonical: baseUrl,
      languages: {
        'zh': baseUrl,
        'en': `${baseUrl}/en`,
      },
    },
  };
}

export default function Home() {
  return (
    <>
      <div className="relative overflow-hidden">
        {/* Hero Section - 主视觉区域 */}
        <HeroSection />

        {/* Services Overview - 业务概览 */}
        <ServicesOverview />

        {/* Stats Section - 数据统计 */}
        <StatsSection />

        {/* Features Section - 核心功能 */}
        <FeaturesSection />

        {/* Process Flow - 业务流程 */}
        <ProcessFlow />

        {/* Technology Stack - 技术栈 */}
        <TechnologyStack />

        {/* Solutions Section - 解决方案 */}
        <SolutionsSection />

        {/* Client Showcase - 客户案例 */}
        <ClientShowcase />

        {/* Testimonials Section - 客户评价 */}
        <TestimonialsSection />

        {/* Partners Section - 合作伙伴 */}
        <PartnersSection />

        {/* CTA Section - 行动召唤 */}
        <CTASection />
      </div>

      {/* 首页结构化数据 */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "WebPage",
            "name": "零点科技首页",
            "description": "零点科技专注于AI智能标注、CPU算力租用、教育培训管理和定制软件开发",
            "url": baseUrl,
            "mainEntity": {
              "@type": "Organization",
              "name": "零点科技",
              "description": "企业级AI与云计算解决方案提供商"
            },
            "breadcrumb": {
              "@type": "BreadcrumbList",
              "itemListElement": [
                {
                  "@type": "ListItem",
                  "position": 1,
                  "name": "首页",
                  "item": baseUrl
                }
              ]
            }
          }),
        }}
      />
    </>
  )
}


