"use client"

import { Linkedin, Mail, Phone, MapPin } from 'lucide-react'
import Link from 'next/link'
import { Button } from './ui/button'

const Footer = () => {
  const currentYear = new Date().getFullYear()

  return (
    <footer className="bg-slate-50 border-t border-slate-200">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        <div className="py-16">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="lg:col-span-2">
              <div className="flex items-center gap-2 mb-6">
                <div className="w-8 h-8 bg-gradient-to-br from-blue-600 to-blue-700 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-sm">0</span>
                </div>
                <span className="text-xl font-bold text-slate-800">零点科技</span>
              </div>
              <p className="text-slate-600 mb-6 leading-relaxed">
                专注AI智能标注、CPU算力租用、教育培训管理、定制软件开发四大核心业务，为企业数字化转型提供专业技术服务。
              </p>
              
              <div className="space-y-3">
                <div className="flex items-center gap-3 text-slate-600">
                  <Phone className="h-4 w-4 text-blue-600" />
                  <span className="text-sm">************</span>
                </div>
                <div className="flex items-center gap-3 text-slate-600">
                  <Mail className="h-4 w-4 text-blue-600" />
                  <span className="text-sm"><EMAIL></span>
                </div>
                <div className="flex items-center gap-3 text-slate-600">
                  <MapPin className="h-4 w-4 text-blue-600" />
                  <span className="text-sm">北京市朝阳区科技园区88号零点大厦</span>
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-sm font-semibold text-slate-800 mb-4">公司</h3>
              <ul className="space-y-3">
                <li>
                  <Link href="/about" className="text-sm text-slate-600 hover:text-blue-600 transition-colors">
                    关于我们
                  </Link>
                </li>
                <li>
                  <Link href="/products" className="text-sm text-slate-600 hover:text-blue-600 transition-colors">
                    产品服务
                  </Link>
                </li>
                <li>
                  <Link href="/news" className="text-sm text-slate-600 hover:text-blue-600 transition-colors">
                    新闻动态
                  </Link>
                </li>
                <li>
                  <Link href="/careers" className="text-sm text-slate-600 hover:text-blue-600 transition-colors">
                    加入我们
                  </Link>
                </li>
              </ul>
            </div>

            <div>
              <h3 className="text-sm font-semibold text-slate-800 mb-4">支持</h3>
              <ul className="space-y-3">
                <li>
                  <Link href="/help" className="text-sm text-slate-600 hover:text-blue-600 transition-colors">
                    帮助中心
                  </Link>
                </li>
                <li>
                  <Link href="/support" className="text-sm text-slate-600 hover:text-blue-600 transition-colors">
                    技术支持
                  </Link>
                </li>
                <li>
                  <Link href="/contact-us" className="text-sm text-slate-600 hover:text-blue-600 transition-colors">
                    联系我们
                  </Link>
                </li>
                <li>
                  <Link href="/privacy" className="text-sm text-slate-600 hover:text-blue-600 transition-colors">
                    隐私政策
                  </Link>
                </li>
              </ul>
            </div>
          </div>
        </div>

        <div className="border-t border-slate-200 py-8">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <p className="text-sm text-slate-500">
              &copy; {currentYear} 零点科技. 保留所有权利.
            </p>
            <div className="flex items-center gap-2">
              <span className="text-sm text-slate-500 mr-2">关注我们:</span>
              <Button variant="ghost" size="icon" className="h-8 w-8" asChild>
                <Link href="https://linkedin.com/company/0dot" target="_blank" rel="noopener noreferrer">
                  <Linkedin className="h-4 w-4" />
                  <span className="sr-only">LinkedIn</span>
                </Link>
              </Button>
              <Button variant="ghost" size="icon" className="h-8 w-8" asChild>
                <Link href="mailto:<EMAIL>">
                  <Mail className="h-4 w-4" />
                  <span className="sr-only">邮箱</span>
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}

export default Footer
